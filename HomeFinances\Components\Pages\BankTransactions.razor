﻿@page "/bank-transactions"
@rendermode InteractiveServer
@attribute [StreamRendering]
@inherits ComponentBase

@using HomeFinances.Models
@using HomeFinances.Services
@using HomeFinances.Store.BeeHiveTransactions

<MudExpansionPanels>
    <MudExpansionPanel>
        <TitleContent>
            <div class="d-flex align-center">
                <MudText Typo="Typo.h5">Bank Transactions (@SortedTransactions.Count())</MudText>
                @if (BeehiveTransactionState.Value.LoadState.Status != LoadingStatusEnum.Loaded)
                {
                    <MudProgressCircular Size="Size.Small" Color="Color.Primary" Indeterminate="true" Class="ml-2" />
                }
                else
                {
                    <MudText Typo="Typo.h6" Class="ml-4" Style="@(SortedTransactions.FirstOrDefault()?.Balance >= 0 ? "color: green;" : "color: red;")">
                        Balance: @(SortedTransactions.FirstOrDefault()?.Balance.ToString("C") ?? "$0.00")
                    </MudText>
                }
            </div>
        </TitleContent>
        <ChildContent>
            @if (BeehiveTransactionState.Value.LoadState.Status == LoadingStatusEnum.Error)
            {
                <MudAlert Severity="Severity.Error">@BeehiveTransactionState.Value.LoadState.ErrorMessage</MudAlert>
            }
            else if (BeehiveTransactionState.Value.LoadState.Status != LoadingStatusEnum.Loaded)
            {
                <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
            }
            else
            {
                <MudTable Items="@SortedTransactions" Dense="true" Hover="true" Bordered="true" Striped="true">
                    <HeaderContent>
                        <MudTh>Date</MudTh>
                        <MudTh>Description</MudTh>
                        <MudTh>Amount</MudTh>
                        <MudTh>Category</MudTh>
                        <MudTh>Subcategory</MudTh>
                        <MudTh>Balance</MudTh>
                        <MudTh>Actions</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>@(context.TransactionDate?.ToShortDateString() ?? "")</MudTd>
                        <MudTd>@context.Description</MudTd>
                        <MudTd Style="@(context.TransactionAmount >= 0 ? "color: green;" : "color: red;")">@context.TransactionAmount.ToString("C")</MudTd>
                        <MudTd>
                            @if (IsRowInEditMode(context))
                            {
                                <MudSelect T="string" @bind-Value="context.Taxonomy.Category">
                                    <MudSelectItem T="string" Value="@null">Select Category</MudSelectItem>
                                    @foreach (var category in CategoryService.GetAllCategoriesAndSubcategories())
                                    {
                                        <MudSelectItem T="string" Value="@category">@category</MudSelectItem>
                                    }
                                </MudSelect>
                            }
                            else
                            {
                                @context.Taxonomy.Category
                            }
                        </MudTd>
                        <MudTd>
                            @if (IsRowInEditMode(context))
                            {
                                <MudSelect T="string" @bind-Value="context.Taxonomy.SubCategory">
                                    <MudSelectItem T="string" Value="@null">Select SubCategory</MudSelectItem>
                                    @foreach (var subCategory in CategoryService.GetSubCategories(context.Taxonomy.Category))
                                    {
                                        <MudSelectItem T="string" Value="@subCategory">@subCategory</MudSelectItem>
                                    }
                                </MudSelect>
                            }
                            else
                            {
                                @context.Taxonomy.SubCategory
                            }
                        </MudTd>
                        <MudTd Style="@(context.Balance >= 0 ? "color: green;" : "color: red;")">@context.Balance.ToString("C")</MudTd>
                        <MudTd>
                            <MudStack Row="true" Spacing="2" AlignItems="AlignItems.Center">
                                @if (IsRowInEditMode(context))
                                {
                                    <MudButton Color="Color.Success" Size="Size.Small" OnClick="@(() => SaveCategoryChanges(context))">Save</MudButton>
                                    <MudButton Color="Color.Error" Size="Size.Small" OnClick="@(() => ToggleEditMode(context))">Cancel</MudButton>
                                }
                                else
                                {
                                    <MudButton Color="Color.Primary" Size="Size.Small" OnClick="@(() => ToggleEditMode(context))">Edit</MudButton>
                                    <MudButton Color="Color.Info" Size="Size.Small" OnClick="@(() => GetAiSuggestion(context))" Disabled="@IsLoadingAiSuggestion(context)">
                                        @if (IsLoadingAiSuggestion(context))
                                        {
                                            <MudProgressCircular Size="Size.Small" Color="Color.Info" Indeterminate="true" />
                                        }
                                        else
                                        {
                                            <MudIcon Icon="@Icons.Material.Filled.AutoAwesome" />
                                        }
                                    </MudButton>
                                }
                            </MudStack>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            }
        </ChildContent>
    </MudExpansionPanel>
</MudExpansionPanels>

