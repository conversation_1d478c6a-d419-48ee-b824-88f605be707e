﻿@page "/category-comparison"
@using HomeFinances.Models
@using HomeFinances.Services
@using HomeFinances.Store.BankTransactions
@using HomeFinances.Store.BeeHiveTransactions
@using HomeFinances.Store.FutureTransactions
@using Fluxor

<MudExpansionPanels>
    <MudExpansionPanel Text="Category Comparison">
        <MudText Typo="Typo.h5">Category Comparison</MudText>

        @if (BankTransactionState.Value.Loading.Status == LoadingStatusEnum.Loading ||
             BeehiveTransactionState.Value.LoadState.Status == LoadingStatusEnum.Loading ||
             FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.Loading)
        {
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        }
        else if (BankTransactionState.Value.Loading.Status == LoadingStatusEnum.Error ||
                 BeehiveTransactionState.Value.LoadState.Status == LoadingStatusEnum.Error ||
                 FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.Error)
        {
            <MudAlert Severity="Severity.Error">Error loading transactions</MudAlert>
        }
        else
        {
            <MudTable Items="@CategoryComparisons" Dense="true" Hover="true" Bordered="true" Striped="true">
                <HeaderContent>
                    <MudTh>Category</MudTh>
                    <MudTh>Bank Total</MudTh>
                    <MudTh>BeeHive Total</MudTh>
                    <MudTh>Future Total</MudTh>
                    <MudTh>Difference</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>@context.Category</MudTd>
                    <MudTd>@context.BankTotal.ToString("C")</MudTd>
                    <MudTd>@context.BeeHiveTotal.ToString("C")</MudTd>
                    <MudTd>@context.FutureTotal.ToString("C")</MudTd>
                    <MudTd>@context.Difference.ToString("C")</MudTd>
                </RowTemplate>
            </MudTable>
        }
    </MudExpansionPanel>
</MudExpansionPanels>

@code {
    [Inject]
    private IState<BankTransactionState> BankTransactionState { get; set; } = default!;

    [Inject]
    private IState<BeeHiveTransactionState> BeehiveTransactionState { get; set; } = default!;

    [Inject]
    private IState<FutureTransactionState> FutureTransactionState { get; set; } = default!;

    [Inject]
    private IDispatcher Dispatcher { get; set; } = default!;

    private List<CategoryComparison> CategoryComparisons { get; set; } = new();

    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (BankTransactionState.Value.Loading.Status == LoadingStatusEnum.NotLoaded)
        {
            Dispatcher.Dispatch(new BankTransactionActions.LoadTransactionsAction());
        }

        if (BeehiveTransactionState.Value.LoadState.Status == LoadingStatusEnum.NotLoaded)
        {
            Dispatcher.Dispatch(new BeeHiveTransactionActions.LoadBeehiveTransactionsAction());
        }

        if (FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.NotLoaded)
        {
            Dispatcher.Dispatch(new FutureTransactionActions.LoadFutureTransactionsAction());
        }

        BankTransactionState.StateChanged += OnStateHasChanged;
        BeehiveTransactionState.StateChanged += OnStateHasChanged;
        FutureTransactionState.StateChanged += OnStateHasChanged;

        UpdateCategoryComparisons();
    }

    private void OnStateHasChanged(object? sender, EventArgs e)
    {
        UpdateCategoryComparisons();
        InvokeAsync(StateHasChanged);
    }

    private void UpdateCategoryComparisons()
    {
        var bankCategories = BankTransactionState.Value.Transactions
            .GroupBy(t => t.Taxonomy.Category)
            .ToDictionary(g => g.Key, g => g.Sum(t => t.TransactionAmount));

        var beehiveCategories = BeehiveTransactionState.Value.Transactions
            .GroupBy(t => t.Taxonomy.Category)
            .ToDictionary(g => g.Key, g => g.Sum(t => t.TransactionAmount));

        var futureCategories = FutureTransactionState.Value.FutureTransactions
            .GroupBy(t => t.Taxonomy.Category)
            .ToDictionary(g => g.Key, g => g.Sum(t => t.TransactionAmount));

        var allCategories = bankCategories.Keys
            .Union(beehiveCategories.Keys)
            .Union(futureCategories.Keys)
            .Distinct();

        CategoryComparisons = allCategories.Select(category => new CategoryComparison
        {
            Category = category,
            BankTotal = bankCategories.GetValueOrDefault(category),
            BeeHiveTotal = beehiveCategories.GetValueOrDefault(category),
            FutureTotal = futureCategories.GetValueOrDefault(category)
        }).ToList();
    }

    public void Dispose()
    {
        BankTransactionState.StateChanged -= OnStateHasChanged;
        BeehiveTransactionState.StateChanged -= OnStateHasChanged;
        FutureTransactionState.StateChanged -= OnStateHasChanged;
    }
}