﻿using Fluxor;
using HomeFinances.Models;
using HomeFinances.Store.RecurringTransactions;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using HomeFinances.Components.Dialogs;
using static HomeFinances.Store.RecurringTransactions.RecurringTransactionActions;

namespace HomeFinances.Components.Pages
{
    public partial class RecurringTransactions : ComponentBase
    {
        [Inject] IState<RecurringTransactionsState> RecurringTransactionsState { get; set; }
        [Inject] IDispatcher Dispatcher { get; set; }
        [Inject] IDialogService DialogService { get; set; }

        private IDialogReference? _dialogReference;

        private IEnumerable<RecurringTransaction> _recurringTransactions =>
            RecurringTransactionsState?.Value?.RecurringTransactions ?? Enumerable.Empty<RecurringTransaction>();

        protected override void OnInitialized()
        {
            RecurringTransactionsState.StateChanged += StateHasChanged;
            Dispatcher.Dispatch(new RecurringTransactionActions.LoadRecurringTransactionsAction());
        }

        private void StateHasChanged(object? sender, EventArgs e)
        {
            _ = InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            RecurringTransactionsState.StateChanged -= StateHasChanged;
        }

        public async Task OpenAddTransactionDialog()
        {
            var options = new DialogOptions { MaxWidth = MaxWidth.Large, FullWidth = false };
            var parameters = new DialogParameters();

            _dialogReference = DialogService.Show<AddRecurringTransactionDialog>("Add Recurring Transaction", parameters, options);
            var result = await _dialogReference.Result; // Wait for the dialog to close and get the result

            if (!result.Canceled && result.Data is RecurringTransaction newTransaction)
            {
                OnSuccessfulTransactionAdd(newTransaction);
            }
        }

        public void UpdateDescription(RecurringTransaction transaction, string newValue)
        {
            transaction.Description = newValue;
            DispatchTransactionUpdate(transaction);
        }

        public void UpdateDaysOfMonth(RecurringTransaction transaction, int newValue)
        {
            transaction.DayOfMonth = newValue;
            DispatchTransactionUpdate(transaction);
        }

        public void UpdateAmount(RecurringTransaction transaction, decimal newValue)
        {
            transaction.Amount = newValue;
            DispatchTransactionUpdate(transaction);
        }

        public void UpdateEndDate(RecurringTransaction transaction, DateTime? newValue)
        {
            transaction.EndDateTime = newValue;
            DispatchTransactionUpdate(transaction);
        }

        public void UpdateIsOutgo(RecurringTransaction transaction, bool newValue)
        {
            transaction.IsOutgo = newValue;
            DispatchTransactionUpdate(transaction);
        }

        private string GetDateText(DateOnly date)
        {
            return date > (new DateOnly(1975, 9, 24)) ? date.ToString("yyyy-MM-dd") : "-";
        }
        private void DispatchTransactionUpdate(RecurringTransaction transaction)
        {
            Dispatcher.Dispatch(new AddOrUpdateRecurringTransactionAction(transaction));
        }


        public void OnSuccessfulTransactionAdd(RecurringTransaction transaction)
        {
            Dispatcher.Dispatch(new AddOrUpdateRecurringTransactionAction(transaction));
            _dialogReference?.Close();
            InvokeAsync(StateHasChanged);
        }

        public void DeleteTransaction(RecurringTransaction transaction)
        {
            Dispatcher.Dispatch(new RecurringTransactionActions.DeleteRecurringTransactionAction(transaction.Id));
        }

        private void RemoveRecurringTransaction(RecurringTransaction transaction)
        {
            Dispatcher.Dispatch(new RecurringTransactionActions.DeleteRecurringTransactionAction(transaction.Id));
        }
    }
}
