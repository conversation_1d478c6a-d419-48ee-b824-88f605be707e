﻿using HomeFinances.Models;

namespace HomeFinances.Store.BankTransactions
{
    public class BankTransactionActions
    {
        public record LoadTransactionsAction;

        public record LoadTransactionsSuccessAction(IEnumerable<BankTransaction> Transactions);
        public record LoadTransactionsFailedAction(string ErrorMessage);

        public record MergeBeehiveTransactionsAction(IEnumerable<BeeHiveTransaction> BeeHiveTransactions);
        public record MergeBeehiveTransactionsSuccessAction(IEnumerable<BankTransaction> BankTransactions);

        public record MergeBeehiveTransactionsFailedAction(string ErrorMessage);
    }
}