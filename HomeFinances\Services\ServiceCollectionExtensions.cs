using System.Text.Json;
using Amazon;
using Amazon.DynamoDBv2;
using Amazon.Runtime;
using Amazon.S3;
using Azure.Core;
using Azure.Data.Tables;
using Azure.Identity;
using Fluxor;
using HomeFinances.Repositories;
using HomeFinances.Store.Account;

namespace HomeFinances.Services;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAppServices(this IServiceCollection services)
    {
        // Add HttpClient
        services.AddHttpClient<ChatGptApiService>(configureClient: (serviceProvider
                                                      , client) =>
                                                  {
                                                      var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                                                      var apiKey = Environment.GetEnvironmentVariable(variable: "OPENAI_API_KEY");
                                                      if (string.IsNullOrEmpty(value: apiKey))
                                                          throw new InvalidOperationException(
                                                              message:
                                                              "OpenAI API key not found in environment variables. Please set OPENAI_API_KEY.");
                                                      client.DefaultRequestHeaders.Add(name: "Authorization"
                                                          , value: $"Bearer {apiKey}");
                                                  });

        services.AddSingleton<TableServiceClient>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<TableServiceClient>>();
            var tableServiceClientOptions = new TableClientOptions
            {
                Retry =
                                                {
                                                    Mode = RetryMode.Exponential, MaxRetries = 3
                                                    , Delay = TimeSpan.FromSeconds(value: 2)
                                                    , MaxDelay = TimeSpan.FromSeconds(value: 32)
                                                }
                                                ,
                Diagnostics =
                                                {
                                                    IsLoggingEnabled = true, IsDistributedTracingEnabled = true
                                                    , ApplicationId = "HomeFinances"
                                                }
            };

            var tableAccountUrl = "https://homefinances.table.core.windows.net/";
            logger.LogInformation(message: "Initializing TableServiceClient with URL: {Url}"
                                  , tableAccountUrl);
            var client = new TableServiceClient(endpoint: new Uri(uriString: tableAccountUrl)
                                                , tokenCredential: new DefaultAzureCredential()
                                                , options: tableServiceClientOptions);
            logger.LogInformation(message: "TableServiceClient initialized successfully");
            return client;
        });

        // Get AWS credentials from environment variable
        Dictionary<string, string> awsConnectionSettings = null;
        string accessKey = null;
        string secretKey = null;
        string regionName = null;
        string bucketName = null;
        var useAwsBackend = false;

        try
        {
            var connectionJson = Environment.GetEnvironmentVariable(variable: "AWS_S3_HOMEFINANCE_CONNECTION");
            if (!string.IsNullOrEmpty(value: connectionJson))
            {
                awsConnectionSettings = JsonSerializer.Deserialize<Dictionary<string, string>>(json: connectionJson);
                if (awsConnectionSettings != null)
                {
                    awsConnectionSettings.TryGetValue(key: "AccessKey"
                                                      , value: out accessKey);
                    awsConnectionSettings.TryGetValue(key: "SecretKey"
                                                      , value: out secretKey);
                    awsConnectionSettings.TryGetValue(key: "Region"
                                                      , value: out regionName);
                    awsConnectionSettings.TryGetValue(key: "BucketName"
                                                      , value: out bucketName);

                    useAwsBackend = !string.IsNullOrEmpty(value: accessKey) &&
                                    !string.IsNullOrEmpty(value: secretKey) &&
                                    !string.IsNullOrEmpty(value: regionName) &&
                                    !string.IsNullOrEmpty(value: bucketName);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(value: $"Error parsing AWS connection settings {ex.Message} {ex.InnerException}");
        }

        // Register AWS DynamoDB client
        services.AddSingleton<IAmazonDynamoDB>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<IAmazonDynamoDB>>();

            if (!useAwsBackend)
            {
                logger.LogError(message: "AWS credentials not properly configured. AWS backend is required.");
                throw new InvalidOperationException(
                    message:
                    "AWS DynamoDB client is required but AWS credentials are not configured. Please set AWS_S3_HOMEFINANCE_CONNECTION environment variable.");
            }

            try
            {
                // Create AWS credentials and client
                RegionEndpoint region;
                try
                {
                    region = RegionEndpoint.GetBySystemName(systemName: regionName);
                    if (region == null)
                    {
                        logger.LogError(message: "Invalid AWS region name: {RegionName}. AWS backend is required."
                                        , regionName);
                        throw new InvalidOperationException(
                            message: $"Invalid AWS region: {regionName}. AWS backend is required.");
                    }
                }
                catch (Exception regionEx)
                {
                    logger.LogError(exception: regionEx
                                    , message:
                                    "Error getting AWS region endpoint for {RegionName}. AWS backend is required."
                                    , regionName);
                    throw new InvalidOperationException(message: $"Invalid AWS region: {regionName}. AWS backend is required."
                                                        , innerException: regionEx);
                }

                try
                {
                    var credentials = new BasicAWSCredentials(accessKey: accessKey
                                                              , secretKey: secretKey);
                    var clientConfig = new AmazonDynamoDBConfig
                    {
                        RegionEndpoint = region,
                        Timeout = TimeSpan.FromSeconds(value: 10),
                        MaxErrorRetry = 3
                    };

                    logger.LogInformation(message: "Successfully configured AWS DynamoDB client with region: {Region}"
                                          , regionName);
                    return new AmazonDynamoDBClient(credentials: credentials
                                                    , clientConfig: clientConfig);
                }
                catch (Exception clientEx)
                {
                    logger.LogError(exception: clientEx
                                    , message: "Error creating AmazonDynamoDBClient. AWS backend is required.");
                    throw new InvalidOperationException(message: "Failed to create AWS DynamoDB client. AWS backend is required."
                                                        , innerException: clientEx);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(exception: ex
                                , message:
                                "Unexpected error configuring AWS DynamoDB client. AWS backend is required.");
                throw new InvalidOperationException(
                    message: "Unexpected error configuring AWS DynamoDB client. AWS backend is required."
                    , innerException: ex);
            }
        });

        // Register AWS S3 client
        services.AddSingleton<IAmazonS3>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<IAmazonS3>>();

            if (!useAwsBackend)
            {
                logger.LogError(message: "AWS credentials not properly configured. AWS backend is required.");
                throw new InvalidOperationException(
                    message:
                    "AWS S3 client is required but AWS credentials are not configured. Please set AWS_S3_HOMEFINANCE_CONNECTION environment variable.");
            }

            try
            {
                // Create AWS credentials and client
                RegionEndpoint region;
                try
                {
                    region = RegionEndpoint.GetBySystemName(systemName: regionName);
                    if (region == null)
                    {
                        logger.LogError(message: "Invalid AWS region name: {RegionName}. AWS backend is required."
                                        , regionName);
                        throw new InvalidOperationException(
                            message: $"Invalid AWS region: {regionName}. AWS backend is required.");
                    }
                }
                catch (Exception regionEx)
                {
                    logger.LogError(exception: regionEx
                                    , message:
                                    "Error getting AWS region endpoint for {RegionName}. AWS backend is required."
                                    , regionName);
                    throw new InvalidOperationException(message: $"Invalid AWS region: {regionName}. AWS backend is required."
                                                        , innerException: regionEx);
                }

                try
                {
                    var credentials = new BasicAWSCredentials(accessKey: accessKey
                                                              , secretKey: secretKey);
                    var clientConfig = new AmazonS3Config
                    {
                        RegionEndpoint = region,
                        Timeout = TimeSpan.FromSeconds(value: 10),
                        MaxErrorRetry = 3
                    };

                    logger.LogInformation(message: "Successfully configured AWS S3 client with region: {Region}"
                                          , regionName);
                    return new AmazonS3Client(credentials: credentials
                                              , clientConfig: clientConfig);
                }
                catch (Exception clientEx)
                {
                    logger.LogError(exception: clientEx
                                    , message: "Error creating AmazonS3Client. AWS backend is required.");
                    throw new InvalidOperationException(message: "Failed to create AWS S3 client. AWS backend is required."
                                                        , innerException: clientEx);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(exception: ex
                                , message: "Unexpected error configuring AWS S3 client. AWS backend is required.");
                throw new InvalidOperationException(
                    message: "Unexpected error configuring AWS S3 client. AWS backend is required."
                    , innerException: ex);
            }
        });

        // Register repositories - AWS backend is required, no file-based fallbacks
        if (!useAwsBackend)
            throw new InvalidOperationException(
                message:
                "AWS backend is required. Please set AWS_S3_HOMEFINANCE_CONNECTION environment variable with valid credentials.");

        // Register CategoryRepository - AWS DynamoDB required
        services.AddScoped<ICategoryRepository>(implementationFactory: serviceProvider =>
        {
            var dynamoDb = serviceProvider.GetRequiredService<IAmazonDynamoDB>();
            if (dynamoDb == null)
                throw new InvalidOperationException(message: "AWS DynamoDB client is required but not available.");

            var accountStateAccessor = serviceProvider.GetRequiredService<IState<AccountState>>();
            var logger = serviceProvider.GetRequiredService<ILogger<AwsDynamoCategoryRepository>>();
            return new AwsDynamoCategoryRepository(dynamoDb: dynamoDb
                                                   , accountStateAccessor: accountStateAccessor
                                                   , logger: logger);
        });

        // Register BankTransactionRepository - AWS S3 required
        services.AddScoped<IBankTransactionRepository>(implementationFactory: serviceProvider =>
        {
            var s3Client = serviceProvider.GetRequiredService<IAmazonS3>();
            if (s3Client == null)
                throw new InvalidOperationException(message: "AWS S3 client is required but not available.");

            var logger = serviceProvider.GetRequiredService<ILogger<AwsS3BankTransactionRepository>>();
            return new AwsS3BankTransactionRepository(s3Client: s3Client
                                                      , logger: logger);
        });

        // Register RecurringTransactionRepository - AWS S3 required
        services.AddScoped<IRecurringTransactionRepository>(implementationFactory: serviceProvider =>
        {
            var s3Client = serviceProvider.GetRequiredService<IAmazonS3>();
            if (s3Client == null)
                throw new InvalidOperationException(message: "AWS S3 client is required but not available.");

            var logger = serviceProvider.GetRequiredService<ILogger<AwsS3RecurringTransactionRepository>>();
            return new AwsS3RecurringTransactionRepository(s3Client: s3Client
                                                           , logger: logger);
        });

        // Register CategoryMappingRepository - AWS S3 required (create AWS implementation)
        services.AddScoped<ICategoryMappingRepository>(implementationFactory: serviceProvider =>
        {
            var s3Client = serviceProvider.GetRequiredService<IAmazonS3>();
            if (s3Client == null)
                throw new InvalidOperationException(message: "AWS S3 client is required but not available.");

            var logger = serviceProvider.GetRequiredService<ILogger<AwsS3CategoryMappingRepository>>();
            return new AwsS3CategoryMappingRepository(s3Client: s3Client
                                                      , logger: logger);
        });

        // Register services as scoped
        services.AddScoped<ImageGenerationRepository>();
        services.AddScoped<CategoryMappingService>();
        services.AddScoped<CategoryService>();
        services.AddScoped<OpenAiCategoryService>();
        services.AddScoped<TransactionReadWriteService>();
        services.AddScoped<CategorySpendingComparisonService>();
        services.AddScoped<FileService>();

        // Note: Effects should be discovered automatically by Fluxor, not manually registered
        // Removing manual registration of effects as this can cause issues with Fluxor's discovery mechanism
        // services.AddScoped<FutureTransactionEffects>();
        // services.AddScoped<BeeHiveTransactionEffects>();

        // Register AWS S3 File Upload Service
        services.AddScoped<AwsS3FileUploadService>();

        services.AddScoped<IFutureTransactionService, AwsFutureTransactionService>();
        services.AddScoped<IRecurringTransactionService, AwsRecurringTransactionService>();
        services.AddScoped<IIgnoredTransactionsService, AwsIgnoredTransactionService>();
        services.AddScoped<IImageStorageService, AwsImageStorageService>();

        // Add debug log for effect registration
        var loggerFactory = services.BuildServiceProvider().GetService<ILoggerFactory>();
        var logger = loggerFactory?.CreateLogger(categoryName: "ServiceCollectionExtensions");
        logger?.LogInformation(message: "BeeHiveTransactionEffects registered in DI container.");

        return services;
    }
}